#!/bin/bash

# RedHat/CentOS/Fedora 运行脚本
# 用于运行PyQt5设备控制应用程序

echo "=== PyQt5设备控制程序 - RedHat运行脚本 ==="

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装:"
    echo "sudo dnf install python3 python3-pip"
    exit 1
fi

# 检查PyQt5是否安装
python3 -c "import PyQt5" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "PyQt5未安装，正在安装..."
    echo "选择安装方式:"
    echo "1) 使用dnf安装 (推荐)"
    echo "2) 使用pip安装"
    read -p "请选择 (1/2): " choice
    
    case $choice in
        1)
            echo "使用dnf安装PyQt5..."
            sudo dnf install python3-pyqt5
            ;;
        2)
            echo "使用pip安装PyQt5..."
            pip3 install PyQt5
            ;;
        *)
            echo "无效选择，退出"
            exit 1
            ;;
    esac
fi

# 检查显示环境
if [ -z "$DISPLAY" ]; then
    echo "警告: 未设置DISPLAY环境变量"
    echo "如果您使用SSH连接，请使用 ssh -X 或 ssh -Y"
    echo "或者设置 export DISPLAY=:0.0"
fi

echo ""
echo "可用的程序:"
echo "1) M5183BUI.py  - Keysight信号发生器控制"
echo "2) MC2000BUI.py - MC2000B设备控制"
echo "3) 退出"

read -p "请选择要运行的程序 (1/2/3): " app_choice

case $app_choice in
    1)
        echo "启动Keysight信号发生器控制程序..."
        python3 M5183BUI.py
        ;;
    2)
        echo "启动MC2000B设备控制程序..."
        echo "注意: 此程序需要设备库文件，如果没有Linux版本库文件，程序将以模拟模式运行"
        python3 MC2000BUI.py
        ;;
    3)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
