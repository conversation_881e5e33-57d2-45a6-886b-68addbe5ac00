import sys
import traceback
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QPushButton, QTextEdit, QLineEdit, QLabel, QComboBox, QSpinBox,
    QTabWidget, QStatusBar, QProgressBar, QDialog, QMessageBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QTextCursor

# 确保将设备控制库放在同一目录下
try:
    from MC2000B_COMMAND_LIB import *
except Exception as e:
    print(f"无法导入设备库: {e}")


# === 设备控制线程 ===
class DeviceWorker(QThread):
    log_message = pyqtSignal(str)
    operation_complete = pyqtSignal(str, object)
    progress_update = pyqtSignal(int)

    def __init__(self, operation, params=None):
        super().__init__()
        self.operation = operation
        self.params = params or {}

    def run(self):
        try:
            if self.operation == "list_devices":
                self._list_devices()
            elif self.operation == "connect":
                self._connect_device()
            elif self.operation == "get_params":
                self._get_parameters()
            elif self.operation == "set_params":
                self._set_parameters()
            elif self.operation == "close_device":
                self._close_device()
        except Exception as e:
            self.log_message.emit(f"错误: {traceback.format_exc()}")

    def _list_devices(self):
        self.log_message.emit("搜索连接的设备...")
        devs = MC2000BListDevices()
        self.log_message.emit(f"找到 {len(devs)} 个设备")
        self.operation_complete.emit("device_list", devs)

    def _connect_device(self):
        serial_number = self.params.get('serial_number')
        self.log_message.emit(f"尝试连接设备: {serial_number}")

        hdl = MC2000BOpen(serial_number, 115200, 3)
        if hdl < 0:
            self.log_message.emit(f"连接 {serial_number} 失败")
            raise Exception(f"无法连接设备 {serial_number}")

        self.log_message.emit(f"连接 {serial_number} 成功")

        # 验证连接状态
        result = MC2000BIsOpen(serial_number)
        if result < 0:
            self.log_message.emit("验证连接状态失败")
        else:
            self.log_message.emit("设备已连接并就绪")

        # 获取设备信息
        blade_type = c_int(0)
        MC2000BGetBladeType(hdl, blade_type)
        BladeTypeList = {
            0: "MC1F2", 1: "MC1F10", 2: "MC1F15", 3: "MC1F30",
            4: "MC1F60", 5: "MC1F100", 6: "MC1F10HP", 7: "MC1F2P10",
            8: "MC1F6P10", 9: "MC1F10A", 10: "MC2F330", 11: "MC2F47",
            12: "MC2F57B", 13: "MC2F860", 14: "MC2F5360"
        }
        blade_name = BladeTypeList.get(blade_type.value, "未知叶片")
        self.log_message.emit(f"设备类型: {blade_name}")

        self.operation_complete.emit("device_connected", (hdl, serial_number))

    def _get_parameters(self):
        hdl = self.params['hdl']
        self.log_message.emit("获取设备参数...")

        # 初始化参数容器
        params = {
            'blade_type': [0], 'frequency': [0], 'reference': [0],
            'harmonic_multiplier': [0], 'harmonic_divider': [0],
            'phase': [0], 'enable': [0], 'reference_output': [0],
            'display_intensity': [0], 'verbose': [0], 'cycle_adjust': [0]
        }

        # 获取每个参数
        functions = [
            (MC2000BGetBladeType, 'blade_type'),
            (MC2000BGetFrequency, 'frequency'),
            (MC2000BGetReference, 'reference'),
            (MC2000BGetHarmonicMultiplier, 'harmonic_multiplier'),
            (MC2000BGetHarmonicDivider, 'harmonic_divider'),
            (MC2000BGetPhase, 'phase'),
            (MC2000BGetEnable, 'enable'),
            (MC2000BGetReferenceOutput, 'reference_output'),
            (MC2000BGetDisplayIntensity, 'display_intensity'),
            (MC2000BGetVerbose, 'verbose'),
            (MC2000BGetCycleAdjust, 'cycle_adjust')
        ]

        # 执行所有获取参数的函数
        for i, (func, param) in enumerate(functions):
            if func(hdl, params[param]) >= 0:
                self.log_message.emit(f"获取 {param} 成功: {params[param][0]}")
            else:
                self.log_message.emit(f"获取 {param} 失败")
            self.progress_update.emit(int((i + 1) / len(functions) * 100))

        self.log_message.emit("参数获取完成")
        self.operation_complete.emit("parameters_retrieved", params)

    def _set_parameters(self):
        hdl = self.params['hdl']
        settings = self.params['settings']
        self.log_message.emit("配置设备参数...")

        # 映射设置函数
        set_functions = {
            'blade_type': MC2000BSetBladeType,
            'frequency': MC2000BSetFrequency,
            'reference': MC2000BSetReference,
            'harmonic_multiplier': MC2000BSetHarmonicMultiplier,
            'harmonic_divider': MC2000BSetHarmonicDivider,
            'phase': MC2000BSetPhase,
            'enable': MC2000BSetEnable,
            'reference_output': MC2000BSetReferenceOutput,
            'display_intensity': MC2000BSetDisplayIntensity,
            'verbose': MC2000BSetVerbose,
            'cycle_adjust': MC2000BSetCycleAdjust
        }

        # 应用每个设置
        params = list(settings.keys())
        for i, param in enumerate(params):
            value = settings[param]
            if param in set_functions:
                result = set_functions[param](hdl, value)
                if result >= 0:
                    self.log_message.emit(f"设置 {param} 成功: {value}")
                else:
                    self.log_message.emit(f"设置 {param} 失败，返回值: {result}")
                self.progress_update.emit(int((i + 1) / len(params) * 100))

        self.log_message.emit("参数配置完成")
        self.operation_complete.emit("parameters_set", settings)

    def _close_device(self):
        hdl = self.params['hdl']
        self.log_message.emit("关闭设备连接...")
        result = MC2000BClose(hdl)
        if result >= 0:
            self.log_message.emit("设备连接已关闭")
            self.operation_complete.emit("device_closed", None)
        else:
            self.log_message.emit(f"关闭设备失败，错误代码: {result}")


# === 主应用界面 ===
class MC2000BControlApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_device = None  # (hdl, serial_number)
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("MC2000B 设备控制器")
        self.setGeometry(100, 100, 900, 700)

        # 创建主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # 顶部控制面板
        control_panel = QGroupBox("设备控制")
        control_layout = QHBoxLayout()

        # 设备选择框
        self.device_combo = QComboBox()
        self.device_combo.setMinimumWidth(200)
        self.device_combo.addItem("未选择设备", None)
        self.refresh_button = QPushButton("刷新设备")
        self.refresh_button.clicked.connect(self.refresh_devices)

        # 连接控制按钮
        self.connect_button = QPushButton("连接设备")
        self.connect_button.clicked.connect(self.toggle_connection)
        self.disconnect_button = QPushButton("断开连接")
        self.disconnect_button.clicked.connect(self.disconnect_device)
        self.disconnect_button.setEnabled(False)

        control_layout.addWidget(QLabel("选择设备:"))
        control_layout.addWidget(self.device_combo)
        control_layout.addWidget(self.refresh_button)
        control_layout.addWidget(self.connect_button)
        control_layout.addWidget(self.disconnect_button)
        control_panel.setLayout(control_layout)

        # 选项卡区域
        self.tab_widget = QTabWidget()

        # 参数设置标签页
        settings_tab = QWidget()
        settings_layout = QVBoxLayout()

        # 添加参数控制区域
        self.create_settings_form()
        settings_layout.addLayout(self.settings_form)

        # 参数操作按钮
        button_layout = QHBoxLayout()
        self.get_params_button = QPushButton("获取当前参数")
        self.get_params_button.clicked.connect(self.get_parameters)
        self.set_params_button = QPushButton("应用参数设置")
        self.set_params_button.clicked.connect(self.set_parameters)
        self.apply_defaults_button = QPushButton("恢复默认设置")
        self.apply_defaults_button.clicked.connect(self.restore_defaults)

        button_layout.addWidget(self.get_params_button)
        button_layout.addWidget(self.set_params_button)
        button_layout.addWidget(self.apply_defaults_button)

        settings_layout.addLayout(button_layout)
        settings_tab.setLayout(settings_layout)

        # 日志标签页
        log_tab = QWidget()
        log_layout = QVBoxLayout()
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setFont(QFont("Courier", 9))
        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.clicked.connect(self.clear_log)

        log_layout.addWidget(self.log_output)
        log_layout.addWidget(self.clear_log_button)
        log_tab.setLayout(log_layout)

        # 其他功能标签页
        status_tab = QWidget()
        status_layout = QVBoxLayout()
        self.status_info = QTextEdit()
        self.status_info.setReadOnly(True)
        self.status_info.setFont(QFont("Courier", 9))

        status_layout.addWidget(QLabel("设备状态信息:"))
        status_layout.addWidget(self.status_info)
        status_tab.setLayout(status_layout)

        # 添加选项卡
        self.tab_widget.addTab(settings_tab, "参数设置")
        self.tab_widget.addTab(log_tab, "操作日志")
        self.tab_widget.addTab(status_tab, "设备状态")

        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar, 1)

        # 组装主布局
        main_layout.addWidget(control_panel)
        main_layout.addWidget(self.tab_widget)

        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # 初始化日志
        self.log_message("MC2000B 设备控制器已启动")
        self.log_message("请点击'刷新设备'按钮扫描连接的设备")

        # 设置默认状态
        self.set_button_states(False)

    def create_settings_form(self):
        self.settings_form = QVBoxLayout()

        # 创建参数输入控件
        self.params_controls = {}

        # 叶片类型
        blade_group = QGroupBox("叶片设置")
        blade_layout = QHBoxLayout()

        blade_type_label = QLabel("叶片类型:")
        self.blade_type_combo = QComboBox()
        blade_types = {
            0: "MC1F2", 1: "MC1F10", 2: "MC1F15", 3: "MC1F30",
            4: "MC1F60", 5: "MC1F100", 6: "MC1F10HP", 7: "MC1F2P10",
            8: "MC1F6P10", 9: "MC1F10A", 10: "MC2F330", 11: "MC2F47",
            12: "MC2F57B", 13: "MC2F860", 14: "MC2F5360"
        }
        for key, value in blade_types.items():
            self.blade_type_combo.addItem(f"{value}", key)
        self.blade_type_combo.setCurrentIndex(9)  # MC1F10A
        blade_layout.addWidget(blade_type_label)
        blade_layout.addWidget(self.blade_type_combo)

        blade_group.setLayout(blade_layout)
        self.params_controls['blade_type'] = self.blade_type_combo

        # 频率设置
        freq_group = QGroupBox("频率设置 (Hz)")
        freq_layout = QHBoxLayout()

        freq_label = QLabel("频率:")
        self.freq_spinner = QSpinBox()
        self.freq_spinner.setRange(1, 10000)
        self.freq_spinner.setValue(20)
        freq_layout.addWidget(freq_label)
        freq_layout.addWidget(self.freq_spinner)

        freq_layout.addStretch(1)

        ref_label = QLabel("参考源:")
        self.ref_combo = QComboBox()
        ref_modes = {0: "内部", 1: "外部"}
        for key, value in ref_modes.items():
            self.ref_combo.addItem(value, key)
        self.ref_combo.setCurrentIndex(1)  # 外部
        freq_layout.addWidget(ref_label)
        freq_layout.addWidget(self.ref_combo)

        ref_output_label = QLabel("输出模式:")
        self.ref_output_combo = QComboBox()
        output_modes = {0: "目标频率", 1: "实际频率"}
        for key, value in output_modes.items():
            self.ref_output_combo.addItem(value, key)
        self.ref_output_combo.setCurrentIndex(1)  # 实际频率
        freq_layout.addWidget(ref_output_label)
        freq_layout.addWidget(self.ref_output_combo)

        freq_group.setLayout(freq_layout)
        self.params_controls['frequency'] = self.freq_spinner
        self.params_controls['reference'] = self.ref_combo
        self.params_controls['reference_output'] = self.ref_output_combo

        # 谐波设置
        harmonic_group = QGroupBox("谐波设置")
        harmonic_layout = QHBoxLayout()

        mult_label = QLabel("谐波乘数:")
        self.mult_spinner = QSpinBox()
        self.mult_spinner.setRange(1, 10)
        self.mult_spinner.setValue(6)
        harmonic_layout.addWidget(mult_label)
        harmonic_layout.addWidget(self.mult_spinner)

        div_label = QLabel("谐波除数:")
        self.div_spinner = QSpinBox()
        self.div_spinner.setRange(1, 10)
        self.div_spinner.setValue(5)
        harmonic_layout.addWidget(div_label)
        harmonic_layout.addWidget(self.div_spinner)

        phase_label = QLabel("相位调整:")
        self.phase_spinner = QSpinBox()
        self.phase_spinner.setRange(-10, 10)
        self.phase_spinner.setValue(2)
        harmonic_layout.addWidget(phase_label)
        harmonic_layout.addWidget(self.phase_spinner)

        harmonic_group.setLayout(harmonic_layout)
        self.params_controls['harmonic_multiplier'] = self.mult_spinner
        self.params_controls['harmonic_divider'] = self.div_spinner
        self.params_controls['phase'] = self.phase_spinner

        # 其他设置
        other_group = QGroupBox("其他设置")
        other_layout = QHBoxLayout()

        enable_label = QLabel("启用状态:")
        self.enable_combo = QComboBox()
        enable_modes = {0: "禁用", 1: "启用"}
        for key, value in enable_modes.items():
            self.enable_combo.addItem(value, key)
        self.enable_combo.setCurrentIndex(0)  # 禁用
        other_layout.addWidget(enable_label)
        other_layout.addWidget(self.enable_combo)

        intensity_label = QLabel("显示亮度:")
        self.intensity_spinner = QSpinBox()
        self.intensity_spinner.setRange(1, 10)
        self.intensity_spinner.setValue(5)
        other_layout.addWidget(intensity_label)
        other_layout.addWidget(self.intensity_spinner)

        cycle_label = QLabel("周期调整:")
        self.cycle_spinner = QSpinBox()
        self.cycle_spinner.setRange(1, 10)
        self.cycle_spinner.setValue(3)
        other_layout.addWidget(cycle_label)
        other_layout.addWidget(self.cycle_spinner)

        verbose_label = QLabel("详细模式:")
        self.verbose_combo = QComboBox()
        verbose_modes = {0: "禁用", 1: "启用"}
        for key, value in verbose_modes.items():
            self.verbose_combo.addItem(value, key)
        self.verbose_combo.setCurrentIndex(0)  # 禁用
        other_layout.addWidget(verbose_label)
        other_layout.addWidget(self.verbose_combo)

        other_group.setLayout(other_layout)
        self.params_controls['enable'] = self.enable_combo
        self.params_controls['display_intensity'] = self.intensity_spinner
        self.params_controls['cycle_adjust'] = self.cycle_spinner
        self.params_controls['verbose'] = self.verbose_combo

        # 添加到表单布局
        self.settings_form.addWidget(blade_group)
        self.settings_form.addWidget(freq_group)
        self.settings_form.addWidget(harmonic_group)
        self.settings_form.addWidget(other_group)

    # === UI 更新方法 ===
    def log_message(self, message):
        self.log_output.append(message)
        self.log_output.moveCursor(QTextCursor.End)

    def clear_log(self):
        self.log_output.clear()
        self.log_message("日志已清空")

    def update_device_list(self, devices):
        self.device_combo.clear()

        if not devices:
            self.device_combo.addItem("未找到设备", None)
            return

        for i, (serial, model) in enumerate(devices):
            self.device_combo.addItem(f"{model} ({serial})", serial)
            if self.current_device and self.current_device[1] == serial:
                self.device_combo.setCurrentIndex(i)

    def set_button_states(self, connected):
        self.connect_button.setText("断开连接" if connected else "连接设备")
        self.disconnect_button.setEnabled(connected)
        self.get_params_button.setEnabled(connected)
        self.set_params_button.setEnabled(connected)
        self.apply_defaults_button.setEnabled(connected)

        # 在未连接时禁用参数控件
        for control in self.params_controls.values():
            control.setEnabled(connected)

    def show_progress(self, visible):
        self.progress_bar.setVisible(visible)

    # === 设备操作方法 ===
    def refresh_devices(self):
        self.log_message("正在扫描连接的设备...")
        self.worker = DeviceWorker("list_devices")
        self.worker.log_message.connect(self.log_message)
        self.worker.operation_complete.connect(self.handle_operation_complete)
        self.worker.start()
        self.show_progress(True)

    def toggle_connection(self):
        if self.current_device:
            self.disconnect_device()
        else:
            self.connect_device()

    def connect_device(self):
        serial = self.device_combo.currentData()
        if not serial:
            QMessageBox.warning(self, "错误", "请先选择要连接的设备")
            return

        self.log_message(f"尝试连接设备: {serial}")
        self.worker = DeviceWorker("connect", {'serial_number': serial})
        self.worker.log_message.connect(self.log_message)
        self.worker.operation_complete.connect(self.handle_operation_complete)
        self.worker.start()
        self.show_progress(True)

    def disconnect_device(self):
        if not self.current_device:
            return

        hdl, serial = self.current_device
        self.worker = DeviceWorker("close_device", {'hdl': hdl})
        self.worker.log_message.connect(self.log_message)
        self.worker.operation_complete.connect(self.handle_operation_complete)
        self.worker.start()
        self.show_progress(True)

    def get_parameters(self):
        if not self.current_device:
            QMessageBox.warning(self, "错误", "设备未连接")
            return

        self.log_message("正在获取设备参数...")
        self.worker = DeviceWorker("get_params", {'hdl': self.current_device[0]})
        self.worker.log_message.connect(self.log_message)
        self.worker.operation_complete.connect(self.handle_operation_complete)
        self.worker.progress_update.connect(self.progress_bar.setValue)
        self.worker.start()
        self.show_progress(True)

    def set_parameters(self):
        if not self.current_device:
            QMessageBox.warning(self, "错误", "设备未连接")
            return

        # 收集表单参数
        settings = {}
        for param, control in self.params_controls.items():
            if isinstance(control, QComboBox):
                settings[param] = control.currentData()
            else:  # QSpinBox
                settings[param] = control.value()

        self.log_message("正在配置设备参数...")
        self.worker = DeviceWorker("set_params", {
            'hdl': self.current_device[0],
            'settings': settings
        })
        self.worker.log_message.connect(self.log_message)
        self.worker.operation_complete.connect(self.handle_operation_complete)
        self.worker.progress_update.connect(self.progress_bar.setValue)
        self.worker.start()
        self.show_progress(True)

    def restore_defaults(self):
        if not self.current_device:
            QMessageBox.warning(self, "错误", "设备未连接")
            return

        reply = QMessageBox.question(
            self,
            "确认",
            "是否恢复设备默认设置?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.log_message("正在恢复设备默认设置...")
            hdl = self.current_device[0]
            result = Restore(hdl)
            if result >= 0:
                self.log_message("设备已恢复出厂设置")
            else:
                self.log_message(f"恢复出厂设置失败，错误代码: {result}")

    # === 操作结果处理 ===
    def handle_operation_complete(self, operation, data):
        self.show_progress(False)
        self.progress_bar.setValue(0)

        if operation == "device_list":
            self.update_device_list(data)

        elif operation == "device_connected":
            self.current_device = data
            self.log_message(f"设备已连接: {self.current_device[1]}")
            self.set_button_states(True)
            self.status_bar.showMessage(f"设备已连接: {self.current_device[1]}")

        elif operation == "device_closed":
            self.log_message("设备已断开连接")
            self.current_device = None
            self.set_button_states(False)
            self.status_bar.showMessage("设备已断开连接")

        elif operation == "parameters_retrieved":
            self.log_message("参数获取完成，更新UI...")
            self.update_params_ui(data)

        elif operation == "parameters_set":
            self.log_message("参数配置完成")

    def update_params_ui(self, params):
        # 更新UI以反映设备当前参数
        for param, value_list in params.items():
            if param in self.params_controls:
                control = self.params_controls[param]
                value = value_list[0]

                if isinstance(control, QComboBox):
                    # 找到匹配的索引
                    for index in range(control.count()):
                        if control.itemData(index) == value:
                            control.setCurrentIndex(index)
                            break
                else:  # QSpinBox
                    control.setValue(value)


# === 应用入口点 ===
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 检查必要的库是否存在
    try:
        from MC2000B_COMMAND_LIB import *

        main_window = MC2000BControlApp()
        main_window.show()
    except ImportError as e:
        print(f"导入错误: {e}")
        QMessageBox.critical(
            None,
            "关键错误",
            f"无法加载设备控制库: {e}\n"
            "请确保MC2000B_COMMAND_LIB.py和MC2000CommandLibWin32.dll存在"
        )
        sys.exit(1)

    sys.exit(app.exec_())