import sys
import socket
from PyQt5.QtWidgets import (QApp<PERSON>, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QGroupBox, QMessageBox)


class KeysightControlApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.sock = None
        self.initUI()

    def initUI(self):
        self.setWindowTitle('Keysight Signal Generator Control')
        self.setGeometry(100, 100, 500, 400)

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 连接设置区域
        connection_group = QGroupBox("设备连接")
        connection_layout = QVBoxLayout()

        # IP地址输入
        ip_layout = QHBoxLayout()
        ip_layout.addWidget(QLabel("IP地址:"))
        self.ip_input = QLineEdit("*************")
        ip_layout.addWidget(self.ip_input)

        # 端口输入
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("端口:"))
        self.port_input = QLineEdit("5025")
        port_layout.addWidget(self.port_input)

        # 连接按钮
        self.connect_btn = QPushButton("连接设备")
        self.connect_btn.clicked.connect(self.connect_device)

        connection_layout.addLayout(ip_layout)
        connection_layout.addLayout(port_layout)
        connection_layout.addWidget(self.connect_btn)
        connection_group.setLayout(connection_layout)

        # 频率设置区域
        freq_group = QGroupBox("频率设置 (GHz)")
        freq_layout = QVBoxLayout()

        freq_set_layout = QHBoxLayout()
        freq_set_layout.addWidget(QLabel("频率:"))
        self.freq_input = QLineEdit("1.0")
        freq_set_layout.addWidget(self.freq_input)
        self.set_freq_btn = QPushButton("设置频率")
        self.set_freq_btn.clicked.connect(self.set_frequency)
        freq_set_layout.addWidget(self.set_freq_btn)

        freq_layout.addLayout(freq_set_layout)
        freq_group.setLayout(freq_layout)

        # 功率设置区域
        power_group = QGroupBox("功率设置 (dBm)")
        power_layout = QVBoxLayout()

        power_set_layout = QHBoxLayout()
        power_set_layout.addWidget(QLabel("功率:"))
        self.power_input = QLineEdit("-10")
        power_set_layout.addWidget(self.power_input)
        self.set_power_btn = QPushButton("设置功率")
        self.set_power_btn.clicked.connect(self.set_power)
        power_set_layout.addWidget(self.set_power_btn)

        power_layout.addLayout(power_set_layout)
        power_group.setLayout(power_layout)

        # 状态控制区域
        control_group = QGroupBox("设备控制")
        control_layout = QVBoxLayout()

        # 状态读取
        status_layout = QHBoxLayout()
        self.read_status_btn = QPushButton("读取当前状态")
        self.read_status_btn.clicked.connect(self.read_status)
        self.status_label = QLabel("状态: 未连接")
        status_layout.addWidget(self.read_status_btn)
        status_layout.addWidget(self.status_label)

        # 输出控制
        output_layout = QHBoxLayout()
        self.output_on_btn = QPushButton("启动输出")
        self.output_on_btn.clicked.connect(self.output_on)
        self.output_off_btn = QPushButton("停止输出")
        self.output_off_btn.clicked.connect(self.output_off)
        output_layout.addWidget(self.output_on_btn)
        output_layout.addWidget(self.output_off_btn)

        # 保存/加载设置
        config_layout = QHBoxLayout()
        self.save_config_btn = QPushButton("保存当前配置")
        self.save_config_btn.clicked.connect(self.save_config)
        self.load_config_btn = QPushButton("加载配置")
        self.load_config_btn.clicked.connect(self.load_config)
        config_layout.addWidget(self.save_config_btn)
        config_layout.addWidget(self.load_config_btn)

        control_layout.addLayout(status_layout)
        control_layout.addLayout(output_layout)
        control_layout.addLayout(config_layout)
        control_group.setLayout(control_layout)

        # 添加到主布局
        main_layout.addWidget(connection_group)
        main_layout.addWidget(freq_group)
        main_layout.addWidget(power_group)
        main_layout.addWidget(control_group)

        # 禁用控制按钮直到连接
        self.set_controls_enabled(False)

    def set_controls_enabled(self, enabled):
        """启用或禁用控制按钮"""
        self.set_freq_btn.setEnabled(enabled)
        self.set_power_btn.setEnabled(enabled)
        self.read_status_btn.setEnabled(enabled)
        self.output_on_btn.setEnabled(enabled)
        self.output_off_btn.setEnabled(enabled)
        self.save_config_btn.setEnabled(enabled)
        self.load_config_btn.setEnabled(enabled)

    def connect_device(self):
        """连接设备"""
        ip = self.ip_input.text()
        port = int(self.port_input.text())

        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(3)
            self.sock.connect((ip, port))

            # 验证连接
            self.send_command("*IDN?")
            response = self.receive_response()
            if response:
                self.status_label.setText(f"已连接: {response}")
                self.set_controls_enabled(True)
                QMessageBox.information(self, "连接成功", f"已成功连接到设备:\n{response}")
            else:
                self.status_label.setText("连接错误: 无响应")
                self.sock.close()
                self.sock = None

        except Exception as e:
            self.status_label.setText(f"连接错误: {str(e)}")
            if self.sock:
                self.sock.close()
                self.sock = None

    def send_command(self, command):
        """发送SCPI命令"""
        if self.sock:
            try:
                self.sock.sendall(f"{command}\n".encode())
            except Exception as e:
                QMessageBox.critical(self, "发送错误", f"命令发送失败: {str(e)}")

    def receive_response(self):
        """接收设备响应"""
        if self.sock:
            try:
                response = self.sock.recv(1024).decode().strip()
                return response
            except socket.timeout:
                return None
            except Exception as e:
                QMessageBox.critical(self, "接收错误", f"响应接收失败: {str(e)}")
                return None
        return None

    def set_frequency(self):
        """设置频率"""
        freq = self.freq_input.text()
        try:
            freq_val = float(freq)
            if freq_val <= 0:
                raise ValueError("频率必须大于0")

            self.send_command(f":FREQ:CW {freq_val}GHz")
            response = self.receive_response()
            if not response or "error" in response.lower():
                QMessageBox.warning(self, "设置频率", f"频率设置可能未成功: {response}")
            else:
                QMessageBox.information(self, "设置频率", f"频率已设置为 {freq} GHz")
        except ValueError as e:
            QMessageBox.warning(self, "输入错误", f"无效的频率值: {str(e)}")

    def set_power(self):
        """设置功率"""
        power = self.power_input.text()
        try:
            power_val = float(power)
            self.send_command(f":POW:AMPL {power_val}dBm")
            response = self.receive_response()
            if not response or "error" in response.lower():
                QMessageBox.warning(self, "设置功率", f"功率设置可能未成功: {response}")
            else:
                QMessageBox.information(self, "设置功率", f"功率已设置为 {power} dBm")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "无效的功率值")

    def read_status(self):
        """读取当前状态"""
        try:
            self.send_command(":FREQ:CW?")
            freq = self.receive_response()

            self.send_command(":POW:AMPL?")
            power = self.receive_response()

            self.send_command(":OUTP:STAT?")
            output = self.receive_response()

            output_status = "开启" if output == "1" else "关闭"

            self.status_label.setText(f"状态: {freq} GHz, {power} dBm, 输出: {output_status}")
            QMessageBox.information(self, "当前状态",
                                    f"当前频率: {freq} GHz\n"
                                    f"当前功率: {power} dBm\n"
                                    f"输出状态: {output_status}")
        except Exception as e:
            QMessageBox.critical(self, "读取错误", f"读取状态失败: {str(e)}")

    def output_on(self):
        """开启输出"""
        self.send_command(":OUTP:STAT ON")
        response = self.receive_response()
        if response == "1":
            QMessageBox.information(self, "输出控制", "射频输出已开启")
        else:
            QMessageBox.warning(self, "输出控制", "开启输出命令可能未成功")

    def output_off(self):
        """关闭输出"""
        self.send_command(":OUTP:STAT OFF")
        response = self.receive_response()
        if response == "0":
            QMessageBox.information(self, "输出控制", "射频输出已关闭")
        else:
            QMessageBox.warning(self, "输出控制", "关闭输出命令可能未成功")

    def save_config(self):
        """保存当前配置"""
        try:
            # 获取当前设置
            self.send_command(":FREQ:CW?")
            freq = self.receive_response()

            self.send_command(":POW:AMPL?")
            power = self.receive_response()

            # 在实际应用中，这里应该将设置保存到设备的存储位置
            # 例如: :MMEM:STOR:STAT "CURRENT_SETTINGS"
            QMessageBox.information(self, "保存配置",
                                    f"当前设置已保存:\n"
                                    f"频率: {freq} GHz\n"
                                    f"功率: {power} dBm")
        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载保存的配置"""
        try:
            # 在实际应用中，这里应该从设备存储加载设置
            # 例如: :MMEM:LOAD:STAT "CURRENT_SETTINGS"

            # 为了演示，我们将直接显示一个消息框
            QMessageBox.information(self, "加载配置", "已加载保存的设备配置")
        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"加载配置失败: {str(e)}")

    def closeEvent(self, event):
        """关闭窗口时断开连接"""
        if self.sock:
            self.sock.close()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = KeysightControlApp()
    window.show()
    sys.exit(app.exec_())